import { Analytics } from '@vercel/analytics/react';
import { dir } from 'i18next';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google';
import { languages } from '../i18n/settings';
import '../../styles/styles.css';
import { Hotjar } from 'nextjs-hotjar';
import { headers } from 'next/headers';
import { agentIsInactive, getAgentByHost } from '../lib/agent';
import { get503Response } from '../lib/response';
import {
    getTheme,
    debug,
} from '../lib/helpers';
import Head from '../components/Head';

export async function generateStaticParams() {
    return languages.map((lng) => ({ lng }))
}

export default async function RootLayout({
    children,
    params
}) {

    const { lng } = await params;

    const headersList = await headers();
    const agent = await getAgentByHost(headersList.get('host'));
    if (agentIsInactive(agent)) {
        return get503Response();
    }
    const theme = getTheme(headersList.get('x-theme'), agent.theme);

    return (
        <html lang={lng} dir={dir(lng)} className={`${theme} h-full`}>

            <Head
                lng={lng}
                agent={agent}
                theme={theme}
            />

            <body className="h-full font-body">
                {children}
                <Analytics />
                <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS} />
                <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM} />
                <SpeedInsights />
                <Hotjar id={process.env.NEXT_PUBLIC_HOTJAR_ID} sv={parseInt(process.env.NEXT_PUBLIC_HOTJAR_VERSION)} />
                <div
                    id="apg-custom-scripts"
                    dangerouslySetInnerHTML={{ __html: agent.custom_scripts }}
                ></div>
            </body>

        </html>
    );
}
