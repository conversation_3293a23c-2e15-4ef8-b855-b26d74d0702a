import { headers } from 'next/headers';
import Body from '../components/Body';
import {
    getAgentByHost,
    agentIsInactive,
} from '../lib/agent';
import {
    getMediaUrl,
    getTheme,
    debug,
} from '../lib/helpers';
import {
    getApiToken,
    getApiUser,
    redirectToLogin
} from '../lib/auth';
import {
    getAgentQuestions,
    getSharedPromptMessages
} from '../lib/db';
import { get503Response } from '../lib/response';

export default async function Page({ params, searchParams }) {

    const { lng } = await params;
    const queryParams = await searchParams;
    const headersList = await headers();

    let userId = queryParams.user ?? null;
    let user = null;
    // if (headersList.get('host') === process.env.NEXT_PUBLIC_AGENT_DOMAIN) {
    //     const token = await getApiToken();
    //     if (token) {
    //         user = await getApiUser(token);
    //         if (!user) {
    //             redirectToLogin(currentUrl);
    //         }
    //         userId = user.id;
    //     } else {
    //         redirectToLogin(currentUrl);
    //     }
    // }

    // Get Agent
    const agent = await getAgentByHost(headersList.get('host'));
    if (agentIsInactive(agent)) {
        return get503Response();
    }
    const theme = getTheme(headersList.get('x-theme'), agent.theme);

    const agentQuestions = await getAgentQuestions(agent.id, lng);

    let sharedMessages = [];
    if (queryParams.share) {
        sharedMessages = await getSharedPromptMessages(queryParams.share, agent.id);
    }

    const bgMedia = agent.background_path ? getMediaUrl(agent.background_path) : null;

    return (

        <div className="h-full">

            <div
                id="app"
                className="
                    h-full
                    bg-white
                    dark:bg-gray-900
                    dark:bg-[url('/assets/images/background-sm-dark.png')]
                    dark:md:bg-[url('/assets/images/background-md-dark.png')]
                    z-100
                "
            >

                {bgMedia && (
                    <video
                        className="h-full w-full absolute z-0 top-0 left-0 object-cover"
                        muted
                        autoPlay
                        loop
                        poster={bgMedia}
                    >
                        <source src={bgMedia} type="video/mp4"/>
                    </video>
                )}

                <div
                    id="notifications"
                    aria-live="assertive"
                    className="
                        pointer-events-none
                        fixed
                        inset-0
                        flex
                        items-end
                        px-4
                        py-6
                        sm:items-start
                        sm:p-6
                    "
                >
                </div>

                <Body
                    lng={lng}
                    agent={agent}
                    queryParams={queryParams}
                    theme={theme}
                    user={user}
                    userId={userId}
                    sharedMessages={sharedMessages}
                    agentQuestions={agentQuestions}
                />

            </div>

        </div>

    );

}
