import { insertC<PERSON>Click } from '../../lib/db';
import { NextResponse } from 'next/server';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { prompt_id, agent_id } = await req.json();

    await insertCtaClick({
        agent_id: agent_id,
        prompt_id: prompt_id,
    });

    return NextResponse.json({ messsage: 'CTA Click Successfully Logged' }, { status: 200 });

}
