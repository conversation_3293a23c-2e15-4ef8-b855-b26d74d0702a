"use strict";module.exports = validate20;module.exports.default = validate20;const schema22 = {"type":"object","properties":{"query":{"type":"string","nullable":false},"limit":{"type":"integer","nullable":true},"filters":{"type":"object","properties":{"model":{"type":"string","nullable":true},"ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"types":{"type":"array","nullable":true,"items":{"type":"string"}},"languages":{"type":"array","nullable":true,"items":{"type":"string"}},"collection_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"contributor_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"category_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"classification_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"external_ids":{"type":"array","nullable":true,"items":{"type":"string"}},"team_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"user_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"is_locked":{"type":"boolean","nullable":true},"min_weight":{"type":"integer","nullable":true},"max_weight":{"type":"integer","nullable":true},"min_rating":{"type":"integer","nullable":true},"max_rating":{"type":"integer","nullable":true},"min_num_views":{"type":"integer","nullable":true},"max_num_views":{"type":"integer","nullable":true},"min_num_impressions":{"type":"integer","nullable":true},"max_num_impressions":{"type":"integer","nullable":true},"min_num_referrals":{"type":"integer","nullable":true},"max_num_referrals":{"type":"integer","nullable":true},"min_published_on":{"type":"string","nullable":true},"max_published_on":{"type":"string","nullable":true},"min_created_at":{"type":"string","nullable":true},"max_created_at":{"type":"string","nullable":true},"min_updated_at":{"type":"string","nullable":true},"max_updated_at":{"type":"string","nullable":true}}}},"required":["query"],"additionalProperties":false};function validate20(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){let vErrors = null;let errors = 0;if(errors === 0){if(data && typeof data == "object" && !Array.isArray(data)){let missing0;if((data.query === undefined) && (missing0 = "query")){validate20.errors = [{instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty: missing0},message:"must have required property '"+missing0+"'"}];return false;}else {const _errs1 = errors;for(const key0 in data){if(!(((key0 === "query") || (key0 === "limit")) || (key0 === "filters"))){validate20.errors = [{instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty: key0},message:"must NOT have additional properties"}];return false;break;}}if(_errs1 === errors){if(data.query !== undefined){const _errs2 = errors;if(typeof data.query !== "string"){validate20.errors = [{instancePath:instancePath+"/query",schemaPath:"#/properties/query/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid0 = _errs2 === errors;}else {var valid0 = true;}if(valid0){if(data.limit !== undefined){let data1 = data.limit;const _errs5 = errors;if((!(((typeof data1 == "number") && (!(data1 % 1) && !isNaN(data1))) && (isFinite(data1)))) && (data1 !== null)){validate20.errors = [{instancePath:instancePath+"/limit",schemaPath:"#/properties/limit/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid0 = _errs5 === errors;}else {var valid0 = true;}if(valid0){if(data.filters !== undefined){let data2 = data.filters;const _errs8 = errors;if(errors === _errs8){if(data2 && typeof data2 == "object" && !Array.isArray(data2)){if(data2.model !== undefined){let data3 = data2.model;const _errs10 = errors;if((typeof data3 !== "string") && (data3 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/model",schemaPath:"#/properties/filters/properties/model/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs10 === errors;}else {var valid1 = true;}if(valid1){if(data2.ids !== undefined){let data4 = data2.ids;const _errs13 = errors;if((!(Array.isArray(data4))) && (data4 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/ids",schemaPath:"#/properties/filters/properties/ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs13){if(Array.isArray(data4)){var valid2 = true;const len0 = data4.length;for(let i0=0; i0<len0; i0++){let data5 = data4[i0];const _errs16 = errors;if(!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))){validate20.errors = [{instancePath:instancePath+"/filters/ids/" + i0,schemaPath:"#/properties/filters/properties/ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid2 = _errs16 === errors;if(!valid2){break;}}}}var valid1 = _errs13 === errors;}else {var valid1 = true;}if(valid1){if(data2.types !== undefined){let data6 = data2.types;const _errs18 = errors;if((!(Array.isArray(data6))) && (data6 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/types",schemaPath:"#/properties/filters/properties/types/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs18){if(Array.isArray(data6)){var valid3 = true;const len1 = data6.length;for(let i1=0; i1<len1; i1++){const _errs21 = errors;if(typeof data6[i1] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/types/" + i1,schemaPath:"#/properties/filters/properties/types/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid3 = _errs21 === errors;if(!valid3){break;}}}}var valid1 = _errs18 === errors;}else {var valid1 = true;}if(valid1){if(data2.languages !== undefined){let data8 = data2.languages;const _errs23 = errors;if((!(Array.isArray(data8))) && (data8 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/languages",schemaPath:"#/properties/filters/properties/languages/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs23){if(Array.isArray(data8)){var valid4 = true;const len2 = data8.length;for(let i2=0; i2<len2; i2++){const _errs26 = errors;if(typeof data8[i2] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/languages/" + i2,schemaPath:"#/properties/filters/properties/languages/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid4 = _errs26 === errors;if(!valid4){break;}}}}var valid1 = _errs23 === errors;}else {var valid1 = true;}if(valid1){if(data2.collection_ids !== undefined){let data10 = data2.collection_ids;const _errs28 = errors;if((!(Array.isArray(data10))) && (data10 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/collection_ids",schemaPath:"#/properties/filters/properties/collection_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs28){if(Array.isArray(data10)){var valid5 = true;const len3 = data10.length;for(let i3=0; i3<len3; i3++){let data11 = data10[i3];const _errs31 = errors;if(!(((typeof data11 == "number") && (!(data11 % 1) && !isNaN(data11))) && (isFinite(data11)))){validate20.errors = [{instancePath:instancePath+"/filters/collection_ids/" + i3,schemaPath:"#/properties/filters/properties/collection_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid5 = _errs31 === errors;if(!valid5){break;}}}}var valid1 = _errs28 === errors;}else {var valid1 = true;}if(valid1){if(data2.contributor_ids !== undefined){let data12 = data2.contributor_ids;const _errs33 = errors;if((!(Array.isArray(data12))) && (data12 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/contributor_ids",schemaPath:"#/properties/filters/properties/contributor_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs33){if(Array.isArray(data12)){var valid6 = true;const len4 = data12.length;for(let i4=0; i4<len4; i4++){let data13 = data12[i4];const _errs36 = errors;if(!(((typeof data13 == "number") && (!(data13 % 1) && !isNaN(data13))) && (isFinite(data13)))){validate20.errors = [{instancePath:instancePath+"/filters/contributor_ids/" + i4,schemaPath:"#/properties/filters/properties/contributor_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid6 = _errs36 === errors;if(!valid6){break;}}}}var valid1 = _errs33 === errors;}else {var valid1 = true;}if(valid1){if(data2.category_ids !== undefined){let data14 = data2.category_ids;const _errs38 = errors;if((!(Array.isArray(data14))) && (data14 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/category_ids",schemaPath:"#/properties/filters/properties/category_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs38){if(Array.isArray(data14)){var valid7 = true;const len5 = data14.length;for(let i5=0; i5<len5; i5++){let data15 = data14[i5];const _errs41 = errors;if(!(((typeof data15 == "number") && (!(data15 % 1) && !isNaN(data15))) && (isFinite(data15)))){validate20.errors = [{instancePath:instancePath+"/filters/category_ids/" + i5,schemaPath:"#/properties/filters/properties/category_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid7 = _errs41 === errors;if(!valid7){break;}}}}var valid1 = _errs38 === errors;}else {var valid1 = true;}if(valid1){if(data2.classification_ids !== undefined){let data16 = data2.classification_ids;const _errs43 = errors;if((!(Array.isArray(data16))) && (data16 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/classification_ids",schemaPath:"#/properties/filters/properties/classification_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs43){if(Array.isArray(data16)){var valid8 = true;const len6 = data16.length;for(let i6=0; i6<len6; i6++){let data17 = data16[i6];const _errs46 = errors;if(!(((typeof data17 == "number") && (!(data17 % 1) && !isNaN(data17))) && (isFinite(data17)))){validate20.errors = [{instancePath:instancePath+"/filters/classification_ids/" + i6,schemaPath:"#/properties/filters/properties/classification_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid8 = _errs46 === errors;if(!valid8){break;}}}}var valid1 = _errs43 === errors;}else {var valid1 = true;}if(valid1){if(data2.external_ids !== undefined){let data18 = data2.external_ids;const _errs48 = errors;if((!(Array.isArray(data18))) && (data18 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/external_ids",schemaPath:"#/properties/filters/properties/external_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs48){if(Array.isArray(data18)){var valid9 = true;const len7 = data18.length;for(let i7=0; i7<len7; i7++){const _errs51 = errors;if(typeof data18[i7] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/external_ids/" + i7,schemaPath:"#/properties/filters/properties/external_ids/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid9 = _errs51 === errors;if(!valid9){break;}}}}var valid1 = _errs48 === errors;}else {var valid1 = true;}if(valid1){if(data2.team_ids !== undefined){let data20 = data2.team_ids;const _errs53 = errors;if((!(Array.isArray(data20))) && (data20 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/team_ids",schemaPath:"#/properties/filters/properties/team_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs53){if(Array.isArray(data20)){var valid10 = true;const len8 = data20.length;for(let i8=0; i8<len8; i8++){let data21 = data20[i8];const _errs56 = errors;if(!(((typeof data21 == "number") && (!(data21 % 1) && !isNaN(data21))) && (isFinite(data21)))){validate20.errors = [{instancePath:instancePath+"/filters/team_ids/" + i8,schemaPath:"#/properties/filters/properties/team_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid10 = _errs56 === errors;if(!valid10){break;}}}}var valid1 = _errs53 === errors;}else {var valid1 = true;}if(valid1){if(data2.user_ids !== undefined){let data22 = data2.user_ids;const _errs58 = errors;if((!(Array.isArray(data22))) && (data22 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/user_ids",schemaPath:"#/properties/filters/properties/user_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs58){if(Array.isArray(data22)){var valid11 = true;const len9 = data22.length;for(let i9=0; i9<len9; i9++){let data23 = data22[i9];const _errs61 = errors;if(!(((typeof data23 == "number") && (!(data23 % 1) && !isNaN(data23))) && (isFinite(data23)))){validate20.errors = [{instancePath:instancePath+"/filters/user_ids/" + i9,schemaPath:"#/properties/filters/properties/user_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid11 = _errs61 === errors;if(!valid11){break;}}}}var valid1 = _errs58 === errors;}else {var valid1 = true;}if(valid1){if(data2.is_locked !== undefined){let data24 = data2.is_locked;const _errs63 = errors;if((typeof data24 !== "boolean") && (data24 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/is_locked",schemaPath:"#/properties/filters/properties/is_locked/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid1 = _errs63 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_weight !== undefined){let data25 = data2.min_weight;const _errs66 = errors;if((!(((typeof data25 == "number") && (!(data25 % 1) && !isNaN(data25))) && (isFinite(data25)))) && (data25 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_weight",schemaPath:"#/properties/filters/properties/min_weight/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs66 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_weight !== undefined){let data26 = data2.max_weight;const _errs69 = errors;if((!(((typeof data26 == "number") && (!(data26 % 1) && !isNaN(data26))) && (isFinite(data26)))) && (data26 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_weight",schemaPath:"#/properties/filters/properties/max_weight/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs69 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_rating !== undefined){let data27 = data2.min_rating;const _errs72 = errors;if((!(((typeof data27 == "number") && (!(data27 % 1) && !isNaN(data27))) && (isFinite(data27)))) && (data27 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_rating",schemaPath:"#/properties/filters/properties/min_rating/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs72 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_rating !== undefined){let data28 = data2.max_rating;const _errs75 = errors;if((!(((typeof data28 == "number") && (!(data28 % 1) && !isNaN(data28))) && (isFinite(data28)))) && (data28 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_rating",schemaPath:"#/properties/filters/properties/max_rating/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs75 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_num_views !== undefined){let data29 = data2.min_num_views;const _errs78 = errors;if((!(((typeof data29 == "number") && (!(data29 % 1) && !isNaN(data29))) && (isFinite(data29)))) && (data29 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_views",schemaPath:"#/properties/filters/properties/min_num_views/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs78 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_num_views !== undefined){let data30 = data2.max_num_views;const _errs81 = errors;if((!(((typeof data30 == "number") && (!(data30 % 1) && !isNaN(data30))) && (isFinite(data30)))) && (data30 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_views",schemaPath:"#/properties/filters/properties/max_num_views/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs81 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_num_impressions !== undefined){let data31 = data2.min_num_impressions;const _errs84 = errors;if((!(((typeof data31 == "number") && (!(data31 % 1) && !isNaN(data31))) && (isFinite(data31)))) && (data31 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_impressions",schemaPath:"#/properties/filters/properties/min_num_impressions/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs84 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_num_impressions !== undefined){let data32 = data2.max_num_impressions;const _errs87 = errors;if((!(((typeof data32 == "number") && (!(data32 % 1) && !isNaN(data32))) && (isFinite(data32)))) && (data32 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_impressions",schemaPath:"#/properties/filters/properties/max_num_impressions/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs87 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_num_referrals !== undefined){let data33 = data2.min_num_referrals;const _errs90 = errors;if((!(((typeof data33 == "number") && (!(data33 % 1) && !isNaN(data33))) && (isFinite(data33)))) && (data33 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_referrals",schemaPath:"#/properties/filters/properties/min_num_referrals/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs90 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_num_referrals !== undefined){let data34 = data2.max_num_referrals;const _errs93 = errors;if((!(((typeof data34 == "number") && (!(data34 % 1) && !isNaN(data34))) && (isFinite(data34)))) && (data34 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_referrals",schemaPath:"#/properties/filters/properties/max_num_referrals/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs93 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_published_on !== undefined){let data35 = data2.min_published_on;const _errs96 = errors;if((typeof data35 !== "string") && (data35 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_published_on",schemaPath:"#/properties/filters/properties/min_published_on/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs96 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_published_on !== undefined){let data36 = data2.max_published_on;const _errs99 = errors;if((typeof data36 !== "string") && (data36 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_published_on",schemaPath:"#/properties/filters/properties/max_published_on/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs99 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_created_at !== undefined){let data37 = data2.min_created_at;const _errs102 = errors;if((typeof data37 !== "string") && (data37 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_created_at",schemaPath:"#/properties/filters/properties/min_created_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs102 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_created_at !== undefined){let data38 = data2.max_created_at;const _errs105 = errors;if((typeof data38 !== "string") && (data38 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_created_at",schemaPath:"#/properties/filters/properties/max_created_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs105 === errors;}else {var valid1 = true;}if(valid1){if(data2.min_updated_at !== undefined){let data39 = data2.min_updated_at;const _errs108 = errors;if((typeof data39 !== "string") && (data39 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_updated_at",schemaPath:"#/properties/filters/properties/min_updated_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs108 === errors;}else {var valid1 = true;}if(valid1){if(data2.max_updated_at !== undefined){let data40 = data2.max_updated_at;const _errs111 = errors;if((typeof data40 !== "string") && (data40 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_updated_at",schemaPath:"#/properties/filters/properties/max_updated_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs111 === errors;}else {var valid1 = true;}}}}}}}}}}}}}}}}}}}}}}}}}}}}}else {validate20.errors = [{instancePath:instancePath+"/filters",schemaPath:"#/properties/filters/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}var valid0 = _errs8 === errors;}else {var valid0 = true;}}}}}}else {validate20.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}validate20.errors = vErrors;return errors === 0;}