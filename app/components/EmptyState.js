import { track } from '@vercel/analytics';

export default function EmptyState({
    t,
    sendMessage,
    introPreamble,
    introHeadline,
    introDescription,
    agentQuestionsTitle,
    agentQuestions,
}) {

    const promptSampleQuestion = (e) => {
        const btn = e.target;
        const questionId = btn.getAttribute('data-question-id');
        const question = btn.innerText;
        track('Prompt:Sample', { questionId: questionId });
        sendMessage({ text: question });
    };

    return (

        <div className="flex h-full text-center">
            <div
                className="
                    h-full
                    flex
                    flex-col
                    mx-auto
                "
            >

                <div
                    className="
                        m-auto
                        max-w-3xl
                        sm:max-w-4xl
                        md:max-w-5xl
                        lg:max-w-6xl
                        grow
                        flex
                        flex-col
                        items-center
                        justify-center
                    "
                >

                    <h1>
                        <span
                            id="apg-intro-preamble"
                            className="
                                animate-slidein-stagger-1
                                opacity-0
                                block
                                text-xl
                                sm:text-2xl
                                md:text-3xl
                                lg:text-4xl
                                xl:text-5xl
                                text-gray-600
                                dark:text-gray-200
                            "
                        >
                            {introPreamble}
                        </span>
                        <span className="animate-slidein-stagger-2 opacity-0 block">
                            <span
                                id="apg-intro-headline"
                                className="
                                    text-5xl
                                    sm:text-7xl
                                    md:text-8xl
                                    lg:text-9xl
                                    xl:text-10xl
                                    font-display
                                    font-semibold
                                    tracking-tight
                                    text-shadow
                                    text-transparent
                                    gradient-text
                                    animate-gradient
                                "
                            >
                                {introHeadline}
                            </span>
                        </span>
                    </h1>

                    <p
                        id="apg-intro-description"
                        className="
                            animate-slidein-stagger-3
                            opacity-0
                            max-w-2xl
                            mx-auto
                            mt-16
                            text-md
                            sm:text-lg
                            md:text-xl
                            lg:text-2xl
                            leading-8
                            text-gray-500
                            dark:text-gray-400
                        "
                        dangerouslySetInnerHTML={{ __html: introDescription }}
                    >
                    </p>

                </div>

                {agentQuestions && (agentQuestions.length > 0) && (
                    <div
                        className="
                            animate-slidein-stagger-4
                            opacity-0
                            flex-none
                            hidden
                            md:block
                        "
                    >

                        <h2
                            className="
                                mb-4
                                text-gray-500
                                dark:text-gray-400
                            "
                        >
                            {agentQuestionsTitle ?? t('questions.title')}
                        </h2>

                        <div
                            className="
                                grid
                                grid-cols-2
                                lg:grid-cols-4
                                gap-x-2
                                gap-y-2
                            "
                        >

                            {agentQuestions.map((question, i) => (
                                <button
                                    key={i}
                                    data-question-id={question.id}
                                    className="
                                        px-[1rem]
                                        py-[0.5rem]
                                        rounded-md
                                        text-sm
                                        border
                                        w-full
                                        min-h-[3.5rem]
                                        text-gray-600
                                        dark:text-gray-400
                                        hover:text-gray-700
                                        dark:hover:text-gray-200
                                        border-gray-300
                                        dark:border-gray-800
                                        dark:hover:border-gray-700
                                        bg-white/90
                                        dark:bg-gray-900/90
                                        hover:bg-gray-100
                                        dark:hover:bg-gray-800
                                        dark:shadow-md
                                    "
                                    onClick={promptSampleQuestion}
                                >
                                    {question.question}
                                </button>
                            ))}

                        </div>

                    </div>
                )}

            </div>
        </div>
    );
};
