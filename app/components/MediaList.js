"use client";

import { debug } from '../lib/helpers';
import MediaItem from './MediaItem';
import { ArrowPathIcon } from '@heroicons/react/24/solid';

export default function MediaList({
    t,
    mediaItems,
    hasSearchedMedia,
    responding,
    isCurrentPrompt,
    promptId,
    userId,
}) {

    return (
        <>
            {(!responding || !isCurrentPrompt) && ((mediaItems.length > 0) || !hasSearchedMedia) && (
                <div className="apg-media-list my-8">

                    {(mediaItems.length > 0) ? (
                        <>
                            <h3
                                className="
                                    text-center
                                    uppercase
                                    font-light
                                    mb-4
                                "
                            >
                                {t('media.title')}
                            </h3>
                            <ul className="flex flex-wrap gap-8 justify-center justify-items-center !pl-0">
                                {mediaItems.map((item, index) => (
                                    <li key={index} className="sm:w-1/2 md:w-1/3 xl:w-1/4 list-none">
                                        <MediaItem
                                            t={t}
                                            item={item}
                                            promptId={promptId}
                                            userId={userId}
                                        />
                                    </li>
                                ))}
                            </ul>
                        </>
                    ) : (
                        <div className="text-center mt-16">
                            <ArrowPathIcon className="w-8 h-8 animate-spin text-gray-600 dark:text-gray-400 mx-auto" />
                        </div>
                    )}

                </div>
            )}
        </>

    );
};
