"use client";

import { track } from '@vercel/analytics';
import { trackCta } from '../lib/tracking';
import {
    getConversationId,
    getDeviceId,
    getShareUrl,
    replaceTokens
} from '../lib/helpers';

export default function ResponseFooter({
    t,
    lng,
    content,
    currentPromptId,
    agentId,
    userId,
}) {

    const handleCtaClick = () => {
        track('Cta:Click', {agentId: agentId, currentPromptId: currentPromptId});
        trackCta(currentPromptId, agentId);
    };

    if (content) {
        content = replaceTokens(
            content,
            {
                user: userId,
                conversation: getConversationId(),
                device: getDeviceId(),
                share_url: encodeURIComponent(getShareUrl(agentId, getConversationId(), currentPromptId)),
            }
        );
    }

    return (

        <div
            onClick={handleCtaClick}
            className="
                bg-gray-100
                dark:bg-black/25
                text-gray-600
                dark:text-gray-400
                text-center
            "
        >
            <div
                className="mx-auto inline-block text-left"
                dangerouslySetInnerHTML={{__html: content}}
            >
            </div>
        </div>

    );
};
