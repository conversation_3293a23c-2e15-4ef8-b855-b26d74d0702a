import { getAgentByDomain, getAgentToken } from './db';
import {
    debug
} from './helpers';
import { createFireworks } from '@ai-sdk/fireworks';
import { createGroq } from '@ai-sdk/groq';
import { createTogetherAI } from '@ai-sdk/togetherai';
import { createDeepSeek } from '@ai-sdk/deepseek';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createXai } from '@ai-sdk/xai';
import { createOpenAI } from '@ai-sdk/openai';
import { createGoogleGenerativeAI } from '@ai-sdk/google';

export async function getAgentByHost(host) {
    if (host.indexOf(':') > -1) {
        host = host.substring(0, host.indexOf(':'));
    }
    host = host.toLowerCase();
    return await getAgentByDomain(host);
}

export function agentIsInactive(agent) {
    return (!agent || !agent.is_active || !agent.team_is_active);
}

export function parseFontFromUrl(url) {
    try {
        const urlObject = new URL(url);
        const params = new URLSearchParams(urlObject.search);
        const familyParam = params.get('family');
        if (familyParam) {
            const fontName = familyParam.split(':')[0].replace(/\+/g, ' ');
            // debug(fontName);
            return fontName;
        }
    } catch (error) {
        debug(true, 'ERROR', error);
        return null;
    }
    return null;
}

// Get the agent API token ID
export async function getAgentTokenFromHeader(agentId, headers, apiTokenHeader) {

    // debug(token);
    // Fall back to Authorization: Bearer if x-api-key isn't available
    let apiToken = headers.get(apiTokenHeader);
    if (!apiToken) {
        const authorizationHeader = headers.get('Authorization');
        if (authorizationHeader) {
            apiToken = authorizationHeader.replace('Bearer', '').trim();
        }
    }
    if (apiToken) {
        // debug(apiToken);
        const record = await getAgentToken(agentId, apiToken);
        // debug(record);
        if (record) {
            return parseInt(record.id);
        }
    }

    return null;

}

// Get the agent API token ID
export async function getAgentTokenFromQuery(agentId, params, apiTokenParam) {

    let apiToken = params.get(apiTokenParam);
    if (apiToken) {
        // debug(apiToken);
        const record = await getAgentToken(agentId, apiToken);
        // debug(record);
        if (record) {
            return parseInt(record.id);
        }
    }

    return null;

}

export function getModelProvider(model) {

    // Get the provider config
    const providerKey = model.provider_key ?? process.env.COMPLETION_API_DEFAULT_PROVIDER;
    const modelProviderConfigs = JSON.parse(process.env.COMPLETION_API_PROVIDERS);
    const modelProviderConfig = modelProviderConfigs[providerKey];
    const modelProviderOptions = {
        baseURL: modelProviderConfig['url'],
        apiKey: modelProviderConfig['key'],
    };

    // Set the completion provider service
    const modelProviders = {
        fireworks: createFireworks,
        groq: createGroq,
        together: createTogetherAI,
        deepseek: createDeepSeek,
        anthropic: createAnthropic,
        xai: createXai,
        openai: createOpenAI,
        google: createGoogleGenerativeAI,
    };
    const modelProviderFunc = modelProviders[providerKey] ?? modelProviders.openai; // Default to OpenAI standard if not supported
    // debug(modelProviderFunc);
    const modelProvider = modelProviderFunc(modelProviderOptions);
    // debug(modelProvider);
    modelProvider.deprecations = modelProviderConfig.deprecations ?? [];
    modelProvider.custom = modelProviderConfig.custom ?? {};
    modelProvider.path = modelProviderConfig.path ?? null;

    return modelProvider;

}
