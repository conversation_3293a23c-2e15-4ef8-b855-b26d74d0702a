import { debug } from './helpers';

export async function translateToLanguage(text, from, to = null) {

    if (!to) {
        to = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    }
    if (from === to) {
        return text;
    }

    const translationBody = {
        q: text,
        source: from,
        target: to,
        format: 'text',
    };

    try {
        const translationReq = await callGoogleTranslateApi(translationBody);
        const translationRes = await translationReq.json();
        return translationRes.data.translations[0].translatedText ?? null;
    } catch (e) {
        debug(true, 'TRANSLATION ERROR', e.message);
        debug(true, 'TRANSLATION BODY',translationBody);
        return text;
    }

}

export async function detectLanguage(text) {
    const detectReq = await callGoogleTranslateApi(
        {
            q: text,
        },
        '/detect'
    );
    const detectRes = await detectReq.json();
    const language = detectRes.data.detections[0][0].language ?? process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    debug(true, 'DETECTED LANGUAGE', language);
    return language;
}

async function callGoogleTranslateApi(params, endpoint = null) {
    return await fetch(
        `${process.env.TRANSLATION_API_ENDPOINT}${endpoint}?key=${process.env.TRANSLATION_API_KEY}`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify(params),
        }
    );
}

