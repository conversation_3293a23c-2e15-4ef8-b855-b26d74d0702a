CREATE TYPE "public"."api_response_format" AS ENUM('raw', 'text', 'html', 'json');--> statement-breakpoint
CREATE TYPE "public"."client_enum" AS ENUM('standalone', 'embedded', 'api', 'integration');--> statement-breakpoint
CREATE TYPE "public"."frontend_enum" AS ENUM('agent', 'social');--> statement-breakpoint
CREATE TYPE "public"."model_enum" AS ENUM('collection', 'contributor', 'organization', 'source');--> statement-breakpoint
CREATE TYPE "public"."reasoning_effort_enum" AS ENUM('minimal', 'low', 'medium', 'high');--> statement-breakpoint
CREATE TYPE "public"."theme_enum" AS ENUM('dark', 'light');--> statement-breakpoint
CREATE TYPE "public"."verbosity_enum" AS ENUM('low', 'medium', 'high');--> statement-breakpoint
CREATE SEQUENCE "public"."agent_cta_clicks_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."agent_integration_messages_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."configs_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."evaluations_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."item_impressions_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."item_referrals_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."item_views_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."prompt_sources_prompt_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."prompts_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."shares_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE TABLE "agent_cta_clicks" (
	"id" bigint DEFAULT nextval('agent_cta_clicks_id_seq'::regclass) NOT NULL,
	"agent_id" bigint NOT NULL,
	"prompt_id" bigint NOT NULL,
	"clicked_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_integration_messages" (
	"id" bigint DEFAULT nextval('agent_integration_messages_id_seq'::regclass) NOT NULL,
	"message" varchar(10000) NOT NULL,
	"language" varchar(10) NOT NULL,
	"integration_id" bigint NOT NULL,
	"conversation_id" varchar(100) NOT NULL,
	"user_id" varchar(100),
	"prompted_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_integration_platforms" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(25),
	"description" varchar(500),
	"is_active" boolean DEFAULT false NOT NULL,
	"image_path" varchar(250),
	"endpoint_url" varchar(250),
	"languages" jsonb,
	"has_account" boolean DEFAULT false NOT NULL,
	"has_secret" boolean DEFAULT false NOT NULL,
	"has_cue" boolean DEFAULT false NOT NULL,
	"has_welcome" boolean DEFAULT false NOT NULL,
	"has_cta" boolean DEFAULT false NOT NULL,
	"can_auto_initialize" boolean DEFAULT false NOT NULL,
	"param_1_label" varchar(50),
	"param_2_label" varchar(50),
	"param_3_label" varchar(50),
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_integrations" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
	"agent_id" bigint,
	"platform_id" bigint,
	"is_active" boolean DEFAULT false NOT NULL,
	"account" varchar(250),
	"secret" varchar(500),
	"token" varchar(500),
	"cue" jsonb,
	"welcome" jsonb,
	"cta" jsonb,
	"languages" jsonb,
	"auto_initialize" boolean DEFAULT false NOT NULL,
	"param_1" varchar(500),
	"param_2" varchar(500),
	"param_3" varchar(500),
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_model_providers" (
	"id" bigint NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(255) NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	CONSTRAINT "agent_model_providers_key_check" CHECK ((key)::text = ANY ((ARRAY['anthropic'::character varying, 'fireworks'::character varying, 'groq'::character varying, 'openai'::character varying, 'together'::character varying, 'xai'::character varying])::text[]))
);
--> statement-breakpoint
CREATE TABLE "agent_models" (
	"id" bigint NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(50) NOT NULL,
	"provider_id" bigint,
	"provider_model" varchar(50) NOT NULL,
	"type" varchar(255) NOT NULL,
	"is_recommended" boolean DEFAULT false NOT NULL,
	"max_tokens" integer DEFAULT 4096 NOT NULL,
	"stop_sequence" varchar(50),
	"strip_sequence" varchar(50),
	"languages" jsonb,
	"supports_reasoning_effort" boolean DEFAULT false NOT NULL,
	"supports_verbosity" boolean DEFAULT false NOT NULL,
	"supports_temperature" boolean DEFAULT true NOT NULL,
	"fallback_model_id" bigint,
	"input_cost" numeric(15, 2),
	"output_cost" numeric(15, 2),
	"num_credits" smallint NOT NULL,
	"seq" smallint DEFAULT '0' NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	CONSTRAINT "agent_models_type_check" CHECK ((type)::text = ANY ((ARRAY['limited'::character varying, 'standard'::character varying, 'premium'::character varying, 'reasoning'::character varying, 'admin'::character varying])::text[]))
);
--> statement-breakpoint
CREATE TABLE "agent_questions" (
	"id" bigint NOT NULL,
	"question" jsonb,
	"agent_id" integer NOT NULL,
	"seq" integer DEFAULT 0 NOT NULL,
	"is_active" boolean DEFAULT false,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_tokens" (
	"id" bigint NOT NULL,
	"name" varchar(100) NOT NULL,
	"token" varchar(500),
	"agent_id" integer NOT NULL,
	"is_active" boolean DEFAULT false,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agents" (
	"id" bigint NOT NULL,
	"name" varchar(64) NOT NULL,
	"slug" varchar(64),
	"target_worldview" jsonb,
	"color" char(7) NOT NULL,
	"image_path" varchar(128),
	"model_system_prompt" text,
	"model_max_tokens" integer NOT NULL,
	"model_temperature" double precision NOT NULL,
	"model_top_p" double precision NOT NULL,
	"intro_preamble" jsonb,
	"intro_headline" jsonb,
	"intro_description" jsonb,
	"persona_name" jsonb,
	"persona_tagline" jsonb,
	"persona_description" jsonb,
	"persona_avatar_path" varchar(128),
	"is_active" boolean DEFAULT false,
	"seq" integer DEFAULT 0 NOT NULL,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"theme" "theme_enum" DEFAULT 'dark' NOT NULL,
	"categories" jsonb,
	"max_memories" smallint,
	"collections" jsonb,
	"sources" jsonb,
	"supported_languages" jsonb,
	"supported_translations" jsonb,
	"default_language" varchar(5),
	"default_translation" varchar(5),
	"disable_community_corpus" boolean DEFAULT false,
	"is_locked" boolean DEFAULT false,
	"user_id" bigint,
	"team_id" bigint,
	"vanity_domain" varchar(64),
	"response_footer_content" jsonb,
	"response_footer_threshold" smallint,
	"model_frequency_penalty" double precision DEFAULT 0 NOT NULL,
	"model_presence_penalty" double precision DEFAULT 0 NOT NULL,
	"model_reasoning_effort" "reasoning_effort_enum",
	"model_verbosity" "verbosity_enum",
	"background_path" varchar(255),
	"background_color" char(7),
	"description" jsonb,
	"creator_url" jsonb,
	"is_open_source" boolean DEFAULT false,
	"marketplace_active" boolean DEFAULT false,
	"is_selectable" boolean DEFAULT false,
	"contributors" jsonb,
	"parent_id" integer,
	"is_extensible" boolean DEFAULT false,
	"is_template" boolean DEFAULT false,
	"is_approved" boolean DEFAULT false,
	"model_context_prompt" varchar(1000),
	"use_team_corpus" boolean DEFAULT false,
	"auto_translate" boolean DEFAULT false,
	"auto_translate_languages" jsonb,
	"creator_name" jsonb,
	"custom_styles" varchar(1000),
	"classification_id" integer,
	"root_domain" varchar(50),
	"hide_header" boolean DEFAULT false NOT NULL,
	"footer_text" jsonb,
	"questions_title" jsonb,
	"hide_footer_cta" boolean DEFAULT false NOT NULL,
	"footer_cta_label" jsonb,
	"footer_cta_url" jsonb,
	"model_id" bigint,
	"is_nonbillable" boolean DEFAULT false,
	"meta_title" jsonb,
	"meta_description" jsonb,
	"meta_keywords" jsonb,
	"custom_scripts" varchar(1000),
	"favicon_path" varchar(128),
	"icon_path" varchar(128),
	"icon_color" char(7),
	"display_font_url" varchar(256),
	"body_font_url" varchar(256),
	"beacon_icon_path" varchar(128),
	"show_media" boolean DEFAULT false,
	"has_semantic_search" boolean DEFAULT false,
	"media_collections" jsonb,
	"api_response_format" "api_response_format",
	"has_basic_auth" boolean DEFAULT false,
	"basic_auth_user" varchar(50),
	"basic_auth_password" varchar(500),
	"lock_system_prompt" boolean DEFAULT false,
	"strip_markdown" boolean DEFAULT false,
	"debug" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "categories" (
	"id" bigint NOT NULL,
	"name" varchar(255) NOT NULL,
	"parent_id" bigint,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"slug" varchar(128),
	"tagline" varchar(256),
	"description" text,
	"is_active" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "configs" (
	"id" integer DEFAULT nextval('configs_id_seq'::regclass) NOT NULL,
	"system_prompt" text,
	"max_tokens" integer NOT NULL,
	"temperature" double precision NOT NULL,
	"top_p" double precision NOT NULL,
	"model_key" char(128),
	"model_id" bigint,
	"created_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"agent_id" integer,
	"frequency_penalty" double precision DEFAULT 0 NOT NULL,
	"presence_penalty" double precision DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "evaluations" (
	"id" bigint DEFAULT nextval('evaluations_id_seq'::regclass) NOT NULL,
	"prompt_id" integer,
	"expected_response" text,
	"exp_res_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text),
	"parent_id" integer,
	"flagged" boolean DEFAULT false NOT NULL,
	"accuracy_score" smallint,
	"accuracy_reason" text,
	"sympathy_score" smallint,
	"sympathy_reason" text,
	"eval_model" text,
	"hide" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "item_impressions" (
	"id" bigint DEFAULT nextval('item_impressions_id_seq'::regclass) NOT NULL,
	"frontend" "frontend_enum" NOT NULL,
	"item_model" "model_enum" NOT NULL,
	"item_id" bigint NOT NULL,
	"prompt_id" bigint,
	"user_id" bigint,
	"viewed_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "item_referrals" (
	"id" bigint DEFAULT nextval('item_referrals_id_seq'::regclass) NOT NULL,
	"frontend" "frontend_enum" NOT NULL,
	"item_model" "model_enum" NOT NULL,
	"item_id" bigint NOT NULL,
	"prompt_id" bigint,
	"user_id" bigint,
	"referred_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "item_views" (
	"id" bigint DEFAULT nextval('item_views_id_seq'::regclass) NOT NULL,
	"frontend" "frontend_enum" NOT NULL,
	"item_model" "model_enum" NOT NULL,
	"item_id" bigint NOT NULL,
	"prompt_id" bigint,
	"user_id" bigint,
	"viewed_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "prompt_sources" (
	"prompt_id" bigint DEFAULT nextval('prompt_sources_prompt_id_seq'::regclass) NOT NULL,
	"source_id" bigint NOT NULL,
	"snippet" text NOT NULL,
	"score" double precision
);
--> statement-breakpoint
CREATE TABLE "prompts" (
	"id" bigint DEFAULT nextval('prompts_id_seq'::regclass) NOT NULL,
	"language" varchar(10) NOT NULL,
	"prompt" text NOT NULL,
	"response" text,
	"prompted_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"response_started_at" timestamp(6),
	"response_completed_at" timestamp(6),
	"flagged" boolean DEFAULT false NOT NULL,
	"score" smallint,
	"config_id" integer,
	"session_id" varchar(100),
	"conversation_id" varchar(100),
	"translated_prompt" text,
	"translated_response" text,
	"user_id" varchar(100),
	"agent_id" integer,
	"translation" varchar(4),
	"client" "client_enum" DEFAULT 'standalone',
	"prompt_tokens" integer,
	"response_tokens" integer,
	"agent_token_id" integer,
	"chat_tokens" integer,
	"reasoning" text,
	"liked" boolean DEFAULT false NOT NULL,
	"feedback" text,
	"integration_id" bigint,
	"device_id" varchar(100)
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" varchar(100) NOT NULL,
	"language" varchar(10) NOT NULL,
	"started_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"user_agent" varchar(250),
	"host" varchar(100) NOT NULL,
	"ip_country" char(2),
	"ip_region" varchar(100),
	"ip_city" varchar(100),
	"ip_latitude" real,
	"ip_longitude" real,
	"ip_timezone" varchar(1000),
	"parent_url" varchar(1000),
	"parent_host" varchar(100),
	"user_id" varchar(100)
);
--> statement-breakpoint
CREATE TABLE "shares" (
	"id" bigint DEFAULT nextval('shares_id_seq'::regclass) NOT NULL,
	"shared_prompt_id" bigint NOT NULL,
	"session_id" varchar(100),
	"conversation_id" varchar(100),
	"user_id" varchar(100),
	"agent_id" integer,
	"shared_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sources" (
	"id" bigint NOT NULL,
	"team_id" integer NOT NULL,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL,
	"categories" integer[],
	"collections" integer[],
	"contributors" integer[],
	"classification_id" integer
);
--> statement-breakpoint
CREATE TABLE "teams" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_trial" boolean DEFAULT false NOT NULL,
	"has_custom_corpus" boolean DEFAULT false,
	"corpus_api_key" varchar(500),
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "agent_models" ADD CONSTRAINT "agent_models_provider_id_foreign" FOREIGN KEY ("provider_id") REFERENCES "public"."agent_model_providers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_models" ADD CONSTRAINT "agent_models_fallback_model_id_foreign" FOREIGN KEY ("fallback_model_id") REFERENCES "public"."agent_models"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agents" ADD CONSTRAINT "agents_model_id_foreign" FOREIGN KEY ("model_id") REFERENCES "public"."agent_models"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "prompts_liked_idx" ON "prompts" USING btree ("liked");--> statement-breakpoint
CREATE INDEX "prompts_flagged_idx" ON "prompts" USING btree ("flagged");