const apg={ns:"apg",path:"/beacon/",styles:"styles.min.css",defaultLanguage:"en",offsetX:null,offsetY:null,iconRadius:20,script:null,baseUrl:null,icon:null,iframe:null,language:null,isMobile:window.innerWidth<481,qryParams:["language","languages","translation","translations","theme","color","preamble","headline","description","agent","headless","autoscroll","user",],init:function(){this.setScript(),this.setBaseUrl(),this.setLanguage(),this.addStyles(),this.setOffsets(),this.addIframe(),this.addIcon()},setScript:function(){this.script=document.getElementById(this.nsPrefix("beacon")),console.log(this.script.dataset)},setBaseUrl:function(){let s=this.script.src.indexOf("/",10);this.baseUrl=this.script.src.substring(0,s)},getParam:function(s){let t=null;if(this.script.dataset[s])t=encodeURIComponent(this.script.dataset[s]);else{let i=this.script.src.indexOf(s+"=");if(i>-1){let e=s.length,a=this.script.src.indexOf("&",i+e+1);t=a>-1?this.script.src.substring(i+e+1,a):this.script.src.substring(i+e+1)}}return t},setLanguage:function(){this.language=this.getParam("language"),this.language||(this.language=this.defaultLanguage)},addStyles:function(){let s=document.createElement("link");s.rel="stylesheet",s.type="text/css",s.href=`${this.baseUrl}${this.path}${this.styles}`,document.body.append(s)},setOffsets:function(){this.offsetX=this.getParam("offsetX"),this.offsetY=this.getParam("offsetY")},addIframe:function(){this.iframe=document.createElement("iframe"),this.addClass(this.iframe,this.nsPrefix("iframe")),this.addClass(this.iframe,this.nsPrefix("hidden")),this.isMobile&&this.addClass(this.iframe,this.nsPrefix("mobile"));let s=`${this.baseUrl}/${this.language}`,t="?parent_url="+encodeURIComponent(window.location.href)+"&parent_host="+encodeURIComponent(window.location.host),i=this;this.qryParams.forEach(function(s){let e=i.getParam(s);e&&(t+="&"+s+"="+e)}),s+=t,this.offsetX&&(this.iframe.style.right=this.offsetX),this.offsetY&&(this.iframe.style.bottom=this.offsetY),this.iframe.src=s,document.body.append(this.iframe)},addIcon:function(){this.icon=document.createElement("a"),this.addClass(this.icon,this.nsPrefix("icon")),this.isMobile&&this.addClass(this.icon,this.nsPrefix("mobile")),this.icon.href="#";let s=this;this.icon.addEventListener("click",function(t){t.preventDefault(),s.toggleClass(s.iframe,s.nsPrefix("hidden"))});let t=this.getParam("iconColor")??this.getParam("color");t&&(this.icon.style.backgroundColor=decodeURIComponent(t));let i=this.getParam("iconImage");i&&(this.icon.style.backgroundImage=`url("${decodeURIComponent(i)}")`),this.offsetX&&(this.icon.style.right=this.offsetX,this.icon.style.marginRight=-1*this.iconRadius+"px"),this.offsetY&&(this.icon.style.bottom=this.offsetY,this.icon.style.marginBottom=-1*this.iconRadius+"px"),document.body.append(this.icon)},nsPrefix:function(s){return`${this.ns}-${s}`},addClass:function(s,t){let i=s.className.split(" ");i.push(t),s.className=i.join(" ")},removeClass:function(s,t){s.className=s.className.replace(t,"")},toggleClass:function(s,t){s.className.split(" ").includes(t)?this.removeClass(s,t):this.addClass(s,t)}};apg.init();
